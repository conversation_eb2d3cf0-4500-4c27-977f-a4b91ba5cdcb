# Multi-Profile Instagram Direct Message - User Guide

## 🎯 Overview

The Instagram Direct Message feature has been enhanced to support multiple profiles, allowing you to send messages using multiple Instagram accounts simultaneously. This significantly increases your messaging capacity and efficiency.

## ✨ New Features

### 1. Multi-Profile Selection
- **Select Multiple Profiles**: Choose multiple Instagram profiles from your available accounts
- **Select All Button**: Quickly select or deselect all available profiles
- **Profile List Display**: View selected profiles with their names
- **Real-time Count**: See how many profiles are selected

### 2. Advanced Configuration
- **Users Per Profile**: Set the maximum number of users each profile should message (1-50, default: 40)
- **Allow Duplicate Users**: Choose whether multiple profiles can message the same user
- **Smart Distribution**: Automatic user distribution across selected profiles

### 3. Enhanced Statistics
- **Per-Profile Statistics**: View detailed stats for each profile
- **Success Rate Tracking**: Monitor success rate for each profile
- **Real-time Progress**: Track progress across all profiles
- **Comprehensive Reporting**: Export detailed reports with multi-profile data

## 🚀 How to Use

### Step 1: Upload Recipient List
1. Upload your Excel/CSV file with recipient usernames
2. File format remains the same: `username` and `profilename` columns
3. System will validate and show total recipients

### Step 2: Configure Multi-Profile Settings

#### Profile Selection
1. **Select Profiles**: Check the profiles you want to use for messaging
2. **Use "Select All"**: Click to quickly select all available profiles
3. **Review Selection**: Check the "Selected Profiles" list below

#### Advanced Settings
1. **Users Per Profile**: Set how many users each profile should message
   - Range: 1-50 users
   - Default: 40 users
   - Recommendation: Start with 30-40 for safety

2. **Allow Duplicate Users**: 
   - ✅ **Enabled**: Each profile can message the same users (increases reach)
   - ❌ **Disabled**: Users are distributed uniquely across profiles (no duplicates)

### Step 3: Configure Message Content
1. Enter your message content
2. Set delays between messages (10-30 seconds recommended)
3. Configure image attachments if needed

### Step 4: Start Multi-Profile Messaging
1. Click "Start Instagram Direct Message (X profiles)" button
2. System will automatically:
   - Distribute users across selected profiles
   - Open multiple browser sessions
   - Send messages concurrently
   - Track progress for each profile

## 📊 User Distribution Logic

### Single Profile Mode
- All users assigned to the selected profile
- Limited by "Users Per Profile" setting

### Multi-Profile Mode (No Duplicates)
- Users distributed evenly across profiles
- Each user receives message from only one profile
- Example: 100 users, 3 profiles → ~33 users per profile

### Multi-Profile Mode (Allow Duplicates)
- Each profile gets up to "Users Per Profile" users
- Same users can be messaged by multiple profiles
- Example: 100 users, 3 profiles, 40 per profile → 120 total messages

## 📈 Statistics & Monitoring

### Real-Time Progress
- Overall progress across all profiles
- Individual profile progress
- Success/failure counts per profile

### Detailed Statistics
1. Click "View Results" on completed tasks
2. View per-profile breakdown:
   - Messages sent/failed per profile
   - Success rate per profile
   - Time taken per profile
   - Error messages if any

### Profile Statistics Display
- **Profile Name**: Instagram account name
- **Messages Sent**: Successful messages from this profile
- **Assigned Users**: Total users assigned to this profile
- **Success Rate**: Percentage of successful messages
- **Color Coding**: 
  - 🟢 Green: 80%+ success rate
  - 🟡 Yellow: 50-79% success rate
  - 🔴 Red: <50% success rate

## ⚠️ Best Practices

### Profile Selection
- Start with 2-3 profiles for testing
- Ensure all profiles are properly logged in
- Use profiles with good Instagram standing

### User Distribution
- For safety: 30-40 users per profile maximum
- Enable duplicates only if you want maximum reach
- Monitor success rates and adjust accordingly

### Message Timing
- Use longer delays (20-30 seconds) when using multiple profiles
- Avoid peak hours to reduce detection risk
- Stagger start times if running multiple campaigns

### Error Handling
- Monitor individual profile performance
- If one profile fails, others continue working
- Check error messages in profile statistics

## 🔧 Technical Details

### Database Changes
- New `instagram_dm_profile_statistics` table for per-profile stats
- Enhanced main statistics table with multi-profile support
- Backward compatibility maintained

### API Enhancements
- New endpoint: `/api/instagram/direct-message/profile-statistics/{task_id}`
- Enhanced configuration support
- Concurrent processing with asyncio

### Frontend Improvements
- Multi-select profile interface
- Real-time profile statistics display
- Enhanced progress tracking

## 🐛 Troubleshooting

### Common Issues
1. **Profile Not Showing**: Ensure profile is logged into Instagram
2. **Low Success Rate**: Reduce users per profile or increase delays
3. **Browser Errors**: Check profile cookies and login status

### Error Messages
- **"Profile not found"**: Profile may have been deleted or logged out
- **"No users assigned"**: Check user distribution settings
- **"Browser session failed"**: Profile may need re-login

## 📞 Support

If you encounter issues:
1. Check the profile statistics for detailed error messages
2. Verify all profiles are properly logged in
3. Start with fewer profiles and users for testing
4. Contact support with specific error messages and task IDs

---

**Note**: This feature maintains full backward compatibility. Single-profile workflows continue to work exactly as before.
