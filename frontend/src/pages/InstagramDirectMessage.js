/**
 * Instagram Direct Message Page Component
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Upload, Table, Progress,
  message, Row, Col, InputNumber, Switch, Tag, Modal, Divider, Tooltip,
  Alert, List, Statistic, Steps, Typography, Checkbox
} from 'antd';
import {
  MessageOutlined, UploadOutlined, PlayCircleOutlined, StopOutlined,
  EyeOutlined, DownloadOutlined, DeleteOutlined, ReloadOutlined,
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  FileExcelOutlined, SendOutlined, UpOutlined, DownOutlined, InstagramOutlined,
  SettingOutlined, FolderOutlined, PictureOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;
const { Step } = Steps;

const InstagramDirectMessage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedTasks, setCompletedTasks] = useState([]);
  const [taskProgress, setTaskProgress] = useState({});
  const [shouldStopPolling, setShouldStopPolling] = useState(false);

  // Multi-profile states
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [usersPerProfile, setUsersPerProfile] = useState(40);
  const [allowDuplicateUsers, setAllowDuplicateUsers] = useState(false);
  const [profileStatistics, setProfileStatistics] = useState({});

  // Image attachment states
  const [attachImages, setAttachImages] = useState(false);
  const [imageFolder, setImageFolder] = useState('');
  const [imageFiles, setImageFiles] = useState([]);
  const [imageValidation, setImageValidation] = useState(null);

  // Refs for polling management
  const pollingIntervalRef = useRef(null);
  const pollingCountRef = useRef({});
  const completedTasksRef = useRef(new Set());
  const [completedTasksReloadCount, setCompletedTasksReloadCount] = useState(0);
  const [lastCompletedTasksLoad, setLastCompletedTasksLoad] = useState(0);

  // Debug wrapper functions
  const setActiveTaskWithDebug = (taskId) => {
    console.log('🔄 [InstagramDM] setActiveTask called with:', taskId);
    console.log('🔄 [InstagramDM] Previous activeTask:', activeTask);
    setActiveTask(taskId);
  };

  const setCurrentStepWithDebug = (step) => {
    console.log('🔄 [InstagramDM] setCurrentStep called with:', step);
    console.log('🔄 [InstagramDM] Previous currentStep:', currentStep);
    setCurrentStep(step);
  };

  // Multi-profile helper functions
  const handleSelectAllProfiles = () => {
    if (selectedProfiles.length === profiles.length) {
      setSelectedProfiles([]);
    } else {
      setSelectedProfiles(profiles.map(p => p.id));
    }
  };

  const handleProfileSelection = (profileId) => {
    setSelectedProfiles(prev => {
      if (prev.includes(profileId)) {
        return prev.filter(id => id !== profileId);
      } else {
        return [...prev, profileId];
      }
    });
  };

  const getSelectedProfilesInfo = () => {
    return profiles.filter(p => selectedProfiles.includes(p.id));
  };

  const loadProfileStatistics = async (taskId) => {
    try {
      console.log('📊 [InstagramDM] Loading profile statistics for task:', taskId);
      const response = await apiService.getInstagramDMProfileStatistics(taskId);
      console.log('📊 [InstagramDM] Profile statistics response:', response);

      if (response.profile_statistics) {
        setProfileStatistics(prev => ({
          ...prev,
          [taskId]: response.profile_statistics
        }));
      }
    } catch (error) {
      console.error('❌ [InstagramDM] Failed to load profile statistics:', error);
    }
  };

  // Upload functions
  const uploadFile = async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/instagram/direct-message/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Upload failed');
    }

    return await response.json();
  };

  const uploadImageFiles = async (files) => {
    console.log('🔍 [InstagramDM] uploadImageFiles called with:', files.length, 'files');

    const formData = new FormData();

    // Append all image files
    files.forEach((file, index) => {
      console.log(`🔍 [InstagramDM] Appending file ${index}:`, file.name, file.type, file.size);
      formData.append(`image_${index}`, file);
    });

    console.log('🔍 [InstagramDM] FormData created, making request to backend...');

    const response = await fetch('http://localhost:8000/api/instagram/direct-message/upload-images', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: formData
    });

    console.log('🔍 [InstagramDM] Response received:', response.status, response.statusText);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ [InstagramDM] Upload failed:', errorData);
      throw new Error(errorData.detail || 'Image upload failed');
    }

    const result = await response.json();
    console.log('✅ [InstagramDM] Upload successful:', result);
    return result;
  };

  // Steps configuration
  const steps = [
    {
      title: 'Upload File',
      description: 'Upload Excel file with recipients',
      icon: <UploadOutlined />
    },
    {
      title: 'Configure',
      description: 'Set message content and options',
      icon: <SettingOutlined />
    },
    {
      title: 'Processing',
      description: 'Sending messages in progress',
      icon: <SendOutlined />
    },
    {
      title: 'Complete',
      description: 'Task completed successfully',
      icon: <CheckCircleOutlined />
    }
  ];

  // Polling management
  useEffect(() => {
    console.log('🔄 [InstagramDM] activeTask changed to:', activeTask);
    console.log('🔄 [InstagramDM] shouldStopPolling:', shouldStopPolling);

    if (activeTask && !shouldStopPolling) {
      console.log('🚀 [InstagramDM] Starting polling for task:', activeTask);
      pollingIntervalRef.current = setInterval(() => {
        // Track polling count to prevent infinite polling
        const currentCount = pollingCountRef.current[activeTask] || 0;
        pollingCountRef.current[activeTask] = currentCount + 1;

        console.log(`🔄 [InstagramDM] Polling task status for: ${activeTask} (count: ${currentCount + 1})`);
        console.log('🕐 [InstagramDM] Poll timestamp:', new Date().toISOString());

        // Stop polling after 50 attempts (50 * 3s = 2.5 minutes max)
        if (currentCount >= 50) {
          console.log('🛑 [InstagramDM] Max polling attempts reached, stopping polling');
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
          return;
        }

        pollTaskStatus(activeTask);
      }, 3000);
    } else {
      console.log('🧹 [InstagramDM] Cleaning up polling interval');
    }

    return () => {
      if (pollingIntervalRef.current) {
        console.log('🧹 [InstagramDM] Cleanup: clearing polling interval');
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [activeTask, shouldStopPolling]);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadProfiles(),
        loadActiveTasks(),
        loadCompletedTasks()
      ]);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProfiles = async () => {
    try {
      const response = await apiService.get('/api/profiles');
      console.log('✅ [InstagramDM] Loaded profiles:', response.data);
      const profiles = response.data.filter(p => p.instagram_logged_in && p.type === 'instagram');
      setProfiles(profiles || []);
    } catch (error) {
      console.error('Failed to load profiles:', error);
    }
  };

  const loadActiveTasks = async () => {
    try {
      console.log('📋 [InstagramDM] Loading active tasks');

      const response = await apiService.getInstagramDMTasks();

      console.log('📋 [InstagramDM] Active tasks response:', response);

      setTasks(response.tasks || []);

      // Set active task if there's a running task
      const runningTask = response.tasks?.find(task =>
        task.status === 'running' || task.status === 'pending'
      );

      if (runningTask) {
        console.log('🔄 [InstagramDM] Found running task:', runningTask.task_id);
        setActiveTaskWithDebug(runningTask.task_id);
        setCurrentStepWithDebug(2); // Processing step
      } else {
        console.log('📋 [InstagramDM] No running tasks found');
      }
    } catch (error) {
      console.error('❌ [InstagramDM] Failed to load active tasks:', error);
    }
  };

  const loadCompletedTasks = async (autoSetActiveTask = true) => {
    const now = Date.now();
    if (now - lastCompletedTasksLoad < 2000) {
      console.log('🔄 [InstagramDM] Skipping completed tasks reload (debounce)');
      return;
    }
    setLastCompletedTasksLoad(now);

    try {
      console.log('📋 [InstagramDM] Loading completed tasks from database...');

      const response = await apiService.get('/api/instagram/direct-message/statistics?limit=1000');

      console.log('📋 [InstagramDM] Statistics response:', response.data);

      const statistics = response.data.statistics || [];
      console.log('📋 [InstagramDM] Processing statistics:', statistics.length);

      // Convert statistics to completed tasks format
      const newCompletedTasks = statistics.map(stat => ({
        task_id: stat.task_id,
        name: stat.task_name,
        status: stat.status,
        progress: 100,
        created_at: stat.created_at,
        completed_at: stat.completed_at,
        sender_profile_id: stat.sender_profile_id,
        total_recipients: stat.total_recipients,
        sent_count: stat.messages_sent,
        failed_count: stat.messages_failed,
        errors: [],
        // Additional statistics for display
        success_rate: stat.success_rate,
        total_time_formatted: stat.total_time_formatted,
        average_time_per_message: stat.average_time_per_message,
        excel_file_path: stat.excel_file_path,
        message_content: stat.message_content
      }));

      console.log('📋 [InstagramDM] Setting completed tasks:', newCompletedTasks.length);
      setCompletedTasks(newCompletedTasks);

      // Check if current activeTask is now completed
      if (activeTask && autoSetActiveTask) {
        const completedTask = newCompletedTasks.find(task => task.task_id === activeTask);
        if (completedTask && !completedTasksRef.current.has(activeTask)) {
          console.log('🎉 [InstagramDM] Current task completed:', activeTask);
          completedTasksRef.current.add(activeTask);
          setCurrentStepWithDebug(3); // Complete step
          setShouldStopPolling(true);

          // Clear polling
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
        }
      }
    } catch (error) {
      console.error('❌ [InstagramDM] Failed to load completed tasks:', error);
    }
  };

  const handleFileUpload = async (file) => {
    try {
      console.log('📁 [InstagramDM] Starting file upload process');
      console.log('📁 [InstagramDM] File details:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      setLoading(true);

      // Use the new validateInstagramDMFile method
      const result = await apiService.validateInstagramDMFile(file);

      console.log('📁 [InstagramDM] Validation result:', result);
      console.log('📁 [InstagramDM] Validation result JSON:', JSON.stringify(result, null, 2));

      // Handle wrapped response format
      const responseData = result?.data || result;
      console.log('📁 [InstagramDM] Response data:', responseData);
      console.log('📁 [InstagramDM] responseData.valid value:', responseData.valid);
      console.log('📁 [InstagramDM] typeof responseData.valid:', typeof responseData.valid);

      // More robust validation check
      const isValid = responseData && (responseData.valid === true || responseData.valid === 'true' || responseData.valid === 1);
      console.log('🔍 [InstagramDM] Validation check - isValid:', isValid);

      if (isValid) {
        console.log('✅ [InstagramDM] File validation successful');

        const uploadedFileData = {
          file: file,
          file_path: responseData.file_path || file.name,
          total_recipients: responseData.total_recipients,
          url_type_breakdown: responseData.url_type_breakdown
        };

        console.log('📁 [InstagramDM] Setting uploaded file data:', JSON.stringify(uploadedFileData, null, 2));

        setUploadedFile(uploadedFileData);
        setFileValidation(responseData);
        setCurrentStepWithDebug(1);
        message.success(`Validated ${responseData.total_recipients} recipients successfully`);
      } else {
        console.log('❌ [InstagramDM] File validation failed');
        console.log('❌ [InstagramDM] Result object:', JSON.stringify(result, null, 2));
        console.log('❌ [InstagramDM] responseData.valid:', responseData.valid);
        console.log('❌ [InstagramDM] responseData.error:', responseData.error);
        console.log('❌ [InstagramDM] Condition check details:');
        console.log('❌ [InstagramDM] - responseData exists:', !!responseData);
        console.log('❌ [InstagramDM] - responseData.valid === true:', responseData?.valid === true);
        console.log('❌ [InstagramDM] - responseData.valid === "true":', responseData?.valid === 'true');
        console.log('❌ [InstagramDM] - responseData.valid === 1:', responseData?.valid === 1);

        message.error('File validation failed: ' + (responseData?.error || result?.error || 'Unknown error'));
        setFileValidation(responseData);
      }

      return false; // Prevent default upload behavior
    } catch (error) {
      console.error('❌ [InstagramDM] File upload error:', error);
      console.error('❌ [InstagramDM] Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response
      });

      message.error('Failed to validate file: ' + error.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleImageFolderSelect = async (event) => {
    try {
      const files = event.target.files;
      if (!files || files.length === 0) {
        setImageFiles([]);
        setImageValidation(null);
        return;
      }

      console.log('📁 [InstagramDM] Selected files:', files.length);

      // Convert FileList to Array and filter image files
      const fileArray = Array.from(files);
      const imageFileTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      const validImageFiles = fileArray.filter(file =>
        imageFileTypes.includes(file.type.toLowerCase())
      );

      if (validImageFiles.length === 0) {
        message.error('No valid image files found. Please select JPG, PNG, GIF, or WEBP files.');
        setImageFiles([]);
        setImageValidation({ valid: false, error: 'No valid image files found' });
        return;
      }

      // Check file sizes (limit each file to 10MB)
      const oversizedFiles = validImageFiles.filter(file => file.size > 10 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        message.error(`Some files are too large (max 10MB each): ${oversizedFiles.map(f => f.name).join(', ')}`);
        setImageFiles([]);
        setImageValidation({ valid: false, error: 'Some files exceed size limit' });
        return;
      }

      // Set image files and validation
      const fileNames = validImageFiles.map(file => file.name);
      setImageFiles(validImageFiles);
      setImageValidation({
        valid: true,
        totalFiles: validImageFiles.length,
        fileNames: fileNames,
        totalSize: validImageFiles.reduce((sum, file) => sum + file.size, 0)
      });

      message.success(`Selected ${validImageFiles.length} image files successfully`);
      console.log('📁 [InstagramDM] Valid image files:', fileNames);

    } catch (error) {
      console.error('❌ [InstagramDM] Error selecting image folder:', error);
      message.error('Failed to process selected images: ' + error.message);
      setImageFiles([]);
      setImageValidation({ valid: false, error: error.message });
    }
  };

  const handleStartInstagramDM = async (values) => {
    try {
      console.log('🚀 [InstagramDM] Starting Instagram DM task');
      console.log('🚀 [InstagramDM] Form values:', values);
      console.log('🚀 [InstagramDM] Uploaded file:', uploadedFile);
      console.log('🚀 [InstagramDM] attachImages state:', attachImages);
      console.log('🚀 [InstagramDM] imageFiles state:', imageFiles);
      console.log('🚀 [InstagramDM] imageFiles length:', imageFiles?.length);
      console.log('🚀 [InstagramDM] Selected profiles:', selectedProfiles);

      // Validation
      if (selectedProfiles.length === 0) {
        message.error('Please select at least one profile');
        return;
      }

      setLoading(true);

      // Upload images first if attach_images is enabled
      let uploadedImagePaths = null;
      if (attachImages && imageFiles.length > 0) {
        console.log('📤 [InstagramDM] Uploading images to server...');
        try {
          const uploadResponse = await uploadImageFiles(imageFiles);
          if (uploadResponse && uploadResponse.file_paths) {
            uploadedImagePaths = uploadResponse.file_paths;
            console.log('✅ [InstagramDM] Images uploaded successfully:', uploadedImagePaths);
          } else {
            throw new Error('Failed to get file paths from upload response');
          }
        } catch (uploadError) {
          console.error('❌ [InstagramDM] Failed to upload images:', uploadError);
          message.error('Failed to upload images: ' + uploadError.message);
          setLoading(false);
          return;
        }
      }

      const config = {
        name: values.name,
        // Multi-profile configuration
        sender_profile_ids: selectedProfiles,
        users_per_profile: usersPerProfile,
        allow_duplicate_users: allowDuplicateUsers,
        // Legacy single profile support (use first selected profile as fallback)
        sender_profile_id: selectedProfiles[0],
        excel_file_path: uploadedFile?.file_path,
        message_content: values.message_content,
        delay_between_messages_min: values.delay_between_messages_min || 10,
        delay_between_messages_max: values.delay_between_messages_max || 30,
        stop_on_consecutive_failures: values.stop_on_consecutive_failures || 3,
        skip_sent_recipients: values.skip_sent_recipients !== false,
        // Image attachment configuration
        attach_images: attachImages || false,
        image_file_paths: uploadedImagePaths
      };

      console.log('🚀 [InstagramDM] Task configuration:', config);
      console.log('🚀 [InstagramDM] Config attach_images:', config.attach_images);
      console.log('🚀 [InstagramDM] Config image_file_paths:', config.image_file_paths);

      // Use the new startInstagramDirectMessage method
      const result = await apiService.startInstagramDirectMessage(config);

      console.log('🚀 [InstagramDM] Task start result:', result);
      console.log('🚀 [InstagramDM] Setting activeTask to:', result.task_id);
      console.log('🚀 [InstagramDM] Setting currentStep to: 2');

      // Reset completed tasks tracking for new task
      completedTasksRef.current.clear();
      pollingCountRef.current = {};
      setCompletedTasksReloadCount(0);
      setShouldStopPolling(false);

      setActiveTaskWithDebug(result.task_id);
      setCurrentStepWithDebug(2);
      message.success('Instagram Direct Message task started successfully');

      console.log('🚀 [InstagramDM] After setState - activeTask should be:', result.task_id);

      // Reset form but keep current step and active task
      form.resetFields();
      setUploadedFile(null);
      setFileValidation(null);
      setAttachImages(false);
      setImageFiles([]);
      setImageValidation(null);
      setImageFolder('');
      // Reset multi-profile states
      setSelectedProfiles([]);
      setUsersPerProfile(40);
      setAllowDuplicateUsers(false);
      // Don't reset currentStep - keep it at 2 to show progress

    } catch (error) {
      console.error('❌ [InstagramDM] Failed to start task:', error);
      console.error('❌ [InstagramDM] Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response
      });

      message.error('Failed to start Instagram Direct Message: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      console.log('🔄 [InstagramDM] Polling task status for:', taskId);

      const response = await apiService.getInstagramDMStatus(taskId);

      console.log('🔄 [InstagramDM] Poll response for', taskId, ':', response);
      console.log('🔄 [InstagramDM] Response keys:', Object.keys(response));

      if (response.task) {
        const task = response.task;
        console.log('🔄 [InstagramDM] Task status:', task.status);
        console.log('🔄 [InstagramDM] Task progress:', task.progress);

        // Update task progress
        setTaskProgress(prev => ({
          ...prev,
          [taskId]: {
            status: task.status,
            progress: task.progress || 0,
            current_recipient: task.current_recipient,
            total_recipients: task.total_recipients,
            sent_count: task.sent_count || 0,
            failed_count: task.failed_count || 0,
            last_activity: task.last_activity
          }
        }));

        // Check if task is completed
        if (task.status === 'completed' || task.status === 'failed' || task.status === 'stopped') {
          console.log('🎯 [InstagramDM] Task finished with status:', task.status);

          // Stop polling
          setShouldStopPolling(true);
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }

          // Update step to complete
          setCurrentStepWithDebug(3);

          // Reload completed tasks after a short delay
          setTimeout(() => {
            loadCompletedTasks(false); // Don't auto-set activeTask, keep current one
          }, 1000);

          if (task.status === 'completed') {
            message.success('Instagram Direct Message task completed successfully!');
          } else if (task.status === 'failed') {
            message.error('Instagram Direct Message task failed: ' + (task.error || 'Unknown error'));
          } else if (task.status === 'stopped') {
            message.warning('Instagram Direct Message task was stopped');
          }
        }
      }

    } catch (error) {
      console.error('❌ [InstagramDM] Failed to poll task status:', error);
      // If task not found (404), it might be completed, reload completed tasks
      if (error.response && error.response.status === 404) {
        console.log('🔍 [InstagramDM] Task not found in active tasks, checking completed tasks');
        // Task likely completed and moved to completed tasks
        setTimeout(() => {
          loadCompletedTasks(false); // Don't auto-set activeTask, keep current one
        }, 1000);
      }
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      console.log('🛑 [InstagramDM] Stopping task:', taskId);

      await apiService.stopInstagramDM(taskId);

      console.log('✅ [InstagramDM] Task stopped successfully');
      message.success('Task stopped successfully');
      setActiveTaskWithDebug(null);
      setCurrentStepWithDebug(0);
    } catch (error) {
      console.error('❌ [InstagramDM] Failed to stop task:', error);
      message.error('Failed to stop task: ' + error.message);
    }
  };

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1><InstagramOutlined /> Instagram Direct Message</h1>
          <p>Send direct messages to Instagram users with human-like behavior</p>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              setLastCompletedTasksLoad(0); // Reset debounce
              loadInitialData();
            }}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      {/* Progress Steps */}
      <Card style={{ marginBottom: 24 }}>
        {console.log('🔍 [InstagramDM] Rendering Steps with currentStep:', currentStep)}
        <Steps current={currentStep} items={steps} />
      </Card>

      <Row gutter={[16, 16]}>
        {/* Step 1: File Upload */}
        <Col xs={24} lg={12}>
          <Card title="Step 1: Upload Recipient List">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="File Format Support"
                description={
                  <div>
                    <div><strong>CSV Format:</strong> Single 'username' column (exported from Instagram scraping)</div>
                    <div><strong>Excel Format:</strong> Two columns: 'username' and 'profilename'</div>
                    <div>Username should be Instagram usernames without @ symbol.</div>
                  </div>
                }
                type="info"
                showIcon
              />

              <Upload
                beforeUpload={handleFileUpload}
                accept=".csv,.xlsx,.xls"
                showUploadList={false}
                disabled={loading}
              >
                <Button
                  icon={<UploadOutlined />}
                  loading={loading}
                  size="large"
                  block
                >
                  Upload Excel/CSV File
                </Button>
              </Upload>

              {fileValidation && (
                <Card size="small" style={{ marginTop: 16 }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text strong>File Validation Result:</Text>
                      <Tag color={fileValidation.valid ? 'success' : 'error'}>
                        {fileValidation.valid ? 'Valid' : 'Invalid'}
                      </Tag>
                    </div>

                    {fileValidation.valid ? (
                      <>
                        <Statistic
                          title="Total Recipients"
                          value={fileValidation.total_recipients}
                          prefix={<UserOutlined />}
                        />
                        {fileValidation.url_type_breakdown && (
                          <div>
                            <Text strong>Breakdown:</Text>
                            <List
                              size="small"
                              dataSource={Object.entries(fileValidation.url_type_breakdown)}
                              renderItem={([type, count]) => (
                                <List.Item>
                                  <Text>{type}: {count}</Text>
                                </List.Item>
                              )}
                            />
                          </div>
                        )}
                      </>
                    ) : (
                      <Alert
                        message="Validation Error"
                        description={fileValidation.error}
                        type="error"
                        showIcon
                      />
                    )}
                  </Space>
                </Card>
              )}
            </Space>
          </Card>
        </Col>

        {/* Step 2: Configuration */}
        <Col xs={24} lg={12}>
          <Card title="Step 2: Configure Message Settings">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartInstagramDM}
              disabled={!uploadedFile || !fileValidation?.valid}
            >
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name..." />
              </Form.Item>

              <Form.Item
                label="Sender Profiles"
                rules={[{ required: true, message: 'Please select at least one profile' }]}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <Button
                      type={selectedProfiles.length === profiles.length ? "primary" : "default"}
                      size="small"
                      onClick={handleSelectAllProfiles}
                    >
                      {selectedProfiles.length === profiles.length ? 'Deselect All' : 'Select All'}
                    </Button>
                    <Text type="secondary">
                      {selectedProfiles.length} of {profiles.length} profiles selected
                    </Text>
                  </div>

                  <div style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    padding: '8px',
                    maxHeight: '200px',
                    overflowY: 'auto'
                  }}>
                    {profiles.map(profile => (
                      <div key={profile.id} style={{ marginBottom: '4px' }}>
                        <Checkbox
                          checked={selectedProfiles.includes(profile.id)}
                          onChange={() => handleProfileSelection(profile.id)}
                        >
                          {profile.name}
                        </Checkbox>
                      </div>
                    ))}
                  </div>

                  {selectedProfiles.length > 0 && (
                    <Card size="small" title="Selected Profiles" style={{ marginTop: '8px' }}>
                      <List
                        size="small"
                        dataSource={getSelectedProfilesInfo()}
                        renderItem={(profile) => (
                          <List.Item>
                            <Text>{profile.name}</Text>
                          </List.Item>
                        )}
                      />
                    </Card>
                  )}
                </Space>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="users_per_profile"
                    label="Users per Profile"
                    initialValue={40}
                    rules={[
                      { required: true, message: 'Please enter users per profile' },
                      { type: 'number', min: 1, max: 50, message: 'Must be between 1 and 50' }
                    ]}
                  >
                    <InputNumber
                      min={1}
                      max={50}
                      style={{ width: '100%' }}
                      placeholder="Max users per profile"
                      value={usersPerProfile}
                      onChange={setUsersPerProfile}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="allow_duplicate_users"
                    label="Allow Duplicate Users"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Checkbox
                      checked={allowDuplicateUsers}
                      onChange={(e) => setAllowDuplicateUsers(e.target.checked)}
                    >
                      Allow sending to same user from multiple profiles
                    </Checkbox>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="message_content"
                label="Message Content"
                rules={[{ required: true, message: 'Please enter message content' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Enter your message content..."
                />
              </Form.Item>

              {/* Image Attachment Section */}
              <Form.Item
                name="attach_images"
                valuePropName="checked"
                initialValue={false}
              >
                <Checkbox
                  checked={attachImages}
                  onChange={(e) => {
                    setAttachImages(e.target.checked);
                    if (!e.target.checked) {
                      setImageFiles([]);
                      setImageValidation(null);
                      setImageFolder('');
                    }
                  }}
                >
                  <PictureOutlined /> Attach Images
                </Checkbox>
              </Form.Item>

              {attachImages && (
                <Form.Item
                  label="Select Image Folder"
                  help="Select multiple image files (JPG, PNG, GIF, WEBP) to send with each message"
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageFolderSelect}
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                        fontSize: '14px'
                      }}
                    />

                    {imageValidation && (
                      <Card size="small" style={{ marginTop: 8 }}>
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text strong>Image Validation:</Text>
                            <Tag color={imageValidation.valid ? 'success' : 'error'}>
                              {imageValidation.valid ? 'Valid' : 'Invalid'}
                            </Tag>
                          </div>

                          {imageValidation.valid ? (
                            <>
                              <Statistic
                                title="Total Images"
                                value={imageValidation.totalFiles}
                                prefix={<PictureOutlined />}
                              />
                              <div>
                                <Text strong>Total Size:</Text>
                                <Text> {(imageValidation.totalSize / (1024 * 1024)).toFixed(2)} MB</Text>
                              </div>
                              <div>
                                <Text strong>Image Files:</Text>
                                <List
                                  size="small"
                                  dataSource={imageValidation.fileNames}
                                  renderItem={(fileName) => (
                                    <List.Item>
                                      <Text>{fileName}</Text>
                                    </List.Item>
                                  )}
                                  style={{ maxHeight: 150, overflow: 'auto' }}
                                />
                              </div>
                            </>
                          ) : (
                            <Alert
                              message="Image Validation Error"
                              description={imageValidation.error}
                              type="error"
                              showIcon
                            />
                          )}
                        </Space>
                      </Card>
                    )}
                  </Space>
                </Form.Item>
              )}

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_min"
                    label="Min Delay (seconds)"
                    initialValue={10}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_max"
                    label="Max Delay (seconds)"
                    initialValue={30}
                  >
                    <InputNumber min={10} max={600} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="stop_on_consecutive_failures"
                label="Stop after consecutive failures"
                initialValue={3}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="skip_sent_recipients"
                label="Skip already sent recipients"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={loading}
                disabled={!uploadedFile || !fileValidation?.valid || selectedProfiles.length === 0}
                block
                size="large"
              >
                Start Instagram Direct Message ({selectedProfiles.length} profile{selectedProfiles.length !== 1 ? 's' : ''})
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Task Progress */}
      {activeTask && taskProgress[activeTask] && (
        <Card title="Current Task Progress" style={{ marginTop: 24 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="Status"
                value={taskProgress[activeTask].status}
                valueStyle={{
                  color: taskProgress[activeTask].status === 'completed' ? '#3f8600' :
                         taskProgress[activeTask].status === 'failed' ? '#cf1322' : '#1890ff'
                }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Progress"
                value={taskProgress[activeTask].progress}
                suffix="%"
                prefix={<SendOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Sent"
                value={taskProgress[activeTask].sent_count}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Failed"
                value={taskProgress[activeTask].failed_count}
                prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              />
            </Col>
          </Row>

          <Progress
            percent={taskProgress[activeTask].progress}
            status={taskProgress[activeTask].status === 'failed' ? 'exception' : 'active'}
            style={{ marginTop: 16 }}
          />

          {taskProgress[activeTask].current_recipient && (
            <div style={{ marginTop: 16 }}>
              <Text strong>Current Recipient: </Text>
              <Text>{taskProgress[activeTask].current_recipient}</Text>
            </div>
          )}

          <div style={{ marginTop: 16 }}>
            <Button
              danger
              icon={<StopOutlined />}
              onClick={() => handleStopTask(activeTask)}
            >
              Stop Task
            </Button>
          </div>
        </Card>
      )}

      {/* Completed Tasks */}
      {completedTasks.length > 0 && (
        <Card title="Recent Completed Tasks" style={{ marginTop: 24 }}>
          <Table
            dataSource={completedTasks}
            rowKey="task_id"
            pagination={{ pageSize: 5 }}
            columns={[
              {
                title: 'Task Name',
                dataIndex: 'name',
                key: 'name',
              },
              {
                title: 'Status',
                dataIndex: 'status',
                key: 'status',
                render: (status) => (
                  <Tag color={
                    status === 'completed' ? 'success' :
                    status === 'failed' ? 'error' : 'warning'
                  }>
                    {status.toUpperCase()}
                  </Tag>
                ),
              },
              {
                title: 'Recipients',
                key: 'recipients',
                render: (_, record) => (
                  <span>{record.sent_count || 0} / {record.total_recipients || 0}</span>
                ),
              },
              {
                title: 'Completed At',
                dataIndex: 'completed_at',
                key: 'completed_at',
                render: (date) => date ? new Date(date).toLocaleString() : '-',
              },
              {
                title: 'Actions',
                key: 'actions',
                render: (_, record) => (
                  <Space>
                    <Tooltip title="View Results">
                      <Button
                        icon={<EyeOutlined />}
                        size="small"
                        onClick={() => {
                          loadProfileStatistics(record.task_id);
                          message.info('Loading profile statistics...');
                        }}
                      />
                    </Tooltip>
                    <Tooltip title="Download Report">
                      <Button
                        icon={<DownloadOutlined />}
                        size="small"
                        onClick={() => {
                          // TODO: Implement download report
                          message.info('Download report feature coming soon');
                        }}
                      />
                    </Tooltip>
                  </Space>
                ),
              },
            ]}
          />
        </Card>
      )}

      {/* Profile Statistics */}
      {Object.keys(profileStatistics).length > 0 && (
        <Card title="Profile Statistics" style={{ marginTop: 24 }}>
          {Object.entries(profileStatistics).map(([taskId, stats]) => (
            <Card key={taskId} size="small" title={`Task: ${taskId}`} style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                {stats.map((profileStat, index) => (
                  <Col xs={24} sm={12} md={8} lg={6} key={index}>
                    <Card size="small" style={{ textAlign: 'center' }}>
                      <Statistic
                        title={profileStat.profile_name}
                        value={profileStat.messages_sent}
                        suffix={`/ ${profileStat.assigned_users}`}
                        prefix={<UserOutlined />}
                      />
                      <div style={{ marginTop: 8 }}>
                        <Text type="secondary">Success Rate: </Text>
                        <Text strong style={{ color: profileStat.success_rate >= 80 ? '#52c41a' : profileStat.success_rate >= 50 ? '#faad14' : '#ff4d4f' }}>
                          {profileStat.success_rate.toFixed(1)}%
                        </Text>
                      </div>
                      {profileStat.error_message && (
                        <div style={{ marginTop: 4 }}>
                          <Text type="danger" style={{ fontSize: '12px' }}>
                            {profileStat.error_message}
                          </Text>
                        </div>
                      )}
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          ))}
        </Card>
      )}
    </div>
  );
};

export default InstagramDirectMessage;
